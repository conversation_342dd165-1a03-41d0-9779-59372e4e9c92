clear all;
close all;
clc
warning('off','all');

path='D:\dane\TAKT2\KLDSMG_SWKF__235L_20250801\';
addpath(genpath(path));

files=dir([path,'*.csv']);

for i=1:numel(files)
    filenames{i,1}=files(i).name;
end

%% Set up the Import Options and import the data
if ~exist([path,'mats/'],"dir")
    mkdir([path,'mats/']);
end
for i=1:numel(filenames)
    i
    fname=filenames{i,1};
    [titles, date, time, value] = importfile([path,fname]);
    
    utitles=unique(titles);
    
    for j=1:numel(utitles)
        ind = find(titles==utitles(j));
        s=char(utitles(j));
        % vind=strfind(s,'_');
        % try
        %     vname{j,1}=[s(vind(1)+1:vind(2)),s(vind(4)+1:vind(5)-1)];
        % catch
        %     vname{j,1}=[s(vind(1)+1:vind(2)),s(vind(4)+1:end)];
        % end

        str1=strsplit(s,'_');
        vname{j,1}=str1{3}(5:end);
        dt=[char(date(ind)),repmat(' ',numel(ind),1),char(time(ind))];
        dn=datenum(dt,'yyyy/mm/dd HH:MM:SS.FFF');
        eval([vname{j,1},'=[dn,value(ind)];']);
    end
    svname=string(vname)';
    svname2=cellstr(svname);
    a=char(string(time(1)));
    b=char(string(date(1)));
    % matname=strcat(vname{1}(1:7),'_',b(1:4),'_',b(6:7),'_',b(9:10),'_',fname(28:29));
    matname=strcat(str1{2},'_',str1{3}(1:3),'_',b(1:4),'_',b(6:7),'_',b(9:10),'_',fname(28:29));
    svname3={svname2{:},'svname2'};
    save([path,'mats/',matname,'.mat'],svname3{:});
    clear titles date time value vname;
    % a= whos('-regexp','_all$');
end

%% consolidate
if ~exist([path,'cmats/'],"dir")
    mkdir([path,'cmats/']);
end
files=dir([path,'mats\*.mat']);

for i=1:numel(files)
    filenames{i,1}=files(i).name;
end
mnames=cellfun(@(x) x(1:8),filenames,'UniformOutput',false);
umnames=unique(mnames)';
for k=1:numel(umnames)
    clear filenames files;
    umnames{k}
    files=dir([path,'mats\',umnames{k},'*.mat']);
    
    for i=1:numel(files)
        filenames{i,1}=files(i).name;
    end
    %% Set up the Import Options and import the data
    vnames=[];
    for i=1:numel(filenames)

        fname=filenames{i,1};
        S=load([path,'mats/',fname],'svname2');
        vnames=[vnames,S.svname2];

    end
    uvnames=unique(vnames)';

    for i=1:numel(uvnames)
        eval([uvnames{i},'_all=[];']);
        eval([uvnames{i},'=[];']);
    end

    for i=1:numel(filenames)
        fname=filenames{i,1};
        load([path,'mats/',fname]);
        for j=1:numel(uvnames)
            % ind=find(
            eval([uvnames{j},'_all=[',uvnames{j},'_all;',uvnames{j},'];']);
        end
    end

    a= whos('-regexp','.*_all$');
    c=struct2cell(a);
    varnames=c(1,:);
    % varnames=cellfun(@(x) x(9:end),varnames,'UniformOutput',false);
    varnames={varnames{:},'varnames'};
    matname=strcat(umnames{k},'_all');
    save([path,'cmats/',matname,'.mat'],varnames{:});
end