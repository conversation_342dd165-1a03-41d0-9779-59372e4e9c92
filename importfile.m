function [titles, date, time, value] = importfile(filename, dataLines)
%IMPORTFILE Import data from a text file
%  [TITLES, DATE, TIME, VALUE] = IMPORTFILE(FILENAME) reads data from
%  text file FILENAME for the default selection.  Returns the data as
%  column vectors.
%
%  [TITLES, DATE, TIME, VALUE] = IMPORTFILE(FILE, DATALINES) reads data
%  for the specified row interval(s) of text file FILENAME. Specify
%  DATALINES as a positive scalar integer or a N-by-2 array of positive
%  scalar integers for dis-contiguous row intervals.
%
%  Example:
%  [titles, date, time, value] = importfile("D:\GoogleDrive\2021\hackaton\data\KPDSMG_LK3___431P_20151009_15-16.csv", [4, Inf]);
%
%  See also READTABLE.
%
% Auto-generated by MATLAB on 10-Jun-2021 13:00:45

%% Input handling

% If dataLines is not specified, define defaults
if nargin < 2
    dataLines = [4, Inf];
end

%% Set up the Import Options and import the data
opts = delimitedTextImportOptions("NumVariables", 7);

% Specify range and delimiter
opts.DataLines = dataLines;
opts.Delimiter = ["|",";"];

% Specify column names and types
opts.VariableNames = ["titles", "Var2", "date", "time", "Var5", "value", "Var7"];
opts.SelectedVariableNames = ["titles", "date", "time", "value"];
opts.VariableTypes = ["categorical", "string", "categorical", "categorical", "string", "double", "string"];

% Specify file level properties
opts.ExtraColumnsRule = "ignore";
opts.EmptyLineRule = "read";

% Specify variable properties
opts = setvaropts(opts, ["Var2", "Var5", "Var7"], "WhitespaceRule", "preserve");
opts = setvaropts(opts, ["titles", "Var2", "date", "time", "Var5", "value", "Var7"], "EmptyFieldRule", "auto");

% Import the data
tbl = readtable(filename, opts);

%% Convert to output type
titles = tbl.titles;
date = tbl.date;
time = tbl.time;
value = tbl.value;
end